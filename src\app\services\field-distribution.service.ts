import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class FieldDistributionService {

  /**
   * Distribute fields round robin - EXACT original logic
   */
  distributeFieldsRoundRobin(fields: any[], columnCount: number): any[][] {
    const columns: any[][] = Array.from({ length: columnCount }, () => []);
    
    fields.forEach((field, index) => {
      const columnIndex = index % columnCount;
      columns[columnIndex].push(field);
    });
    
    return columns;
  }

  /**
   * Distribute fields in rows (horizontal alignment)
   * This ensures fields are aligned horizontally across columns
   */
  distributeFieldsInRows(fields: any[], columnCount: number): any[][] {
    const columns: any[][] = Array.from({ length: columnCount }, () => []);
    const totalRows = Math.ceil(fields.length / columnCount);
    
    for (let row = 0; row < totalRows; row++) {
      for (let col = 0; col < columnCount; col++) {
        const fieldIndex = row * columnCount + col;
        if (fieldIndex < fields.length) {
          columns[col].push(fields[fieldIndex]);
        }
      }
    }
    
    return columns;
  }

  /**
   * Order fields based on form definition - EXACT original logic
   */
  orderFieldsBasedOnFormDefinition(fields: any[]): any[] {
    if (!fields || !Array.isArray(fields)) {
      return fields;
    }

    // Separate fields with Group "fieldName" and preserve their order
    const fieldNameGroupFields = fields.filter(field => field.Group === "fieldName");

    // Get all other fields (non-fieldName group) and preserve their order
    const otherFields = fields.filter(field => field.Group !== "fieldName");

    // Combine: fieldName group fields first, then other fields in their original order
    return [
      ...fieldNameGroupFields,
      ...otherFields
    ];
  }

  /**
   * Get input class - EXACT original logic
   */
  getInputClass(): string {
    return 'form-field';
  }

  /**
   * Track by field name - EXACT original logic
   */
  trackByFieldName(_index: number, field: any): string {
    return field.fieldName;
  }

  /**
   * Get keys from object - EXACT original logic
   */
  getKeys(option: any): string[] {
    return Object.keys(option);
  }

  /**
   * Check if ID is valid - EXACT original logic
   */
  isIdValid(id: string): boolean {
    return Boolean(id && id.trim() !== '');
  }
}
